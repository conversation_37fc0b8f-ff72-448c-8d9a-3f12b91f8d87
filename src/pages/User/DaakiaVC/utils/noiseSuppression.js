// import {Track } from "livekit-client";
// import { NoiseSuppressionProcessor } from "@shiguredo/noise-suppression";

// const noiseProcessor = useRef(null);

// noiseProcessor.current = new NoiseSuppressionProcessor();
// const processedTrack = await noiseProcessor.current?.startProcessing(
//               localAudioTrack.mediaStreamTrack,
//             );
// if (processedTrack) {
//    await localAudioTrack.replaceTrack(processedTrack, true);
// }



exports.enableNoiseSuppression = async (localAudioTrack, noiseProcessorRef) => {
    console.log("Noise suppresion ",noiseProcessorRef.current.startProcessing);
    console.log("Noise suppresion ",localAudioTrack.mediaStreamTrack);
    try {
        const processedTrack = await noiseProcessorRef.current.startProcessing(
          localAudioTrack.mediaStreamTrack
        );
        if (processedTrack) {
          await localAudioTrack.replaceTrack(processedTrack, true);
        }  
    } catch (error) {
        console.log("Noise suppresion ",error);
    }
};

exports.disableNoiseSuppression = async (localAudioTrack, noiseProcessorRef) => {
  if (noiseProcessorRef.current) {
    noiseProcessorRef.current.stopProcessing();
    noiseProcessorRef.current = null;
  }
};  